<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\Package;
use App\Models\Service;
use App\Models\Activity;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;

class AdminCheckboxTest extends TestCase
{
    use RefreshDatabase;

    protected $admin;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create admin user
        $this->admin = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password')
        ]);
    }

    /** @test */
    public function it_can_update_package_with_unchecked_checkboxes()
    {
        Storage::fake('public');
        
        // Create a package with checkboxes checked
        $package = Package::create([
            'name' => 'Test Package',
            'description' => 'Test Description',
            'features' => 'Feature 1\nFeature 2',
            'is_featured' => 1,
            'is_active' => 1,
            'sort_order' => 1
        ]);

        // Update package with unchecked checkboxes (not sending is_featured and is_active)
        $response = $this->actingAs($this->admin)
            ->put(route('admin.packages.update', $package->id), [
                'name' => 'Updated Package',
                'description' => 'Updated Description',
                'features' => 'Updated Feature 1\nUpdated Feature 2',
                'sort_order' => 2
                // Note: is_featured and is_active are not sent (unchecked)
            ]);

        $response->assertRedirect();
        
        // Refresh package from database
        $package->refresh();
        
        // Assert that checkboxes are now false (0)
        $this->assertEquals(0, $package->is_featured);
        $this->assertEquals(0, $package->is_active);
        $this->assertEquals('Updated Package', $package->name);
    }

    /** @test */
    public function it_can_update_service_with_unchecked_checkbox()
    {
        Storage::fake('public');
        
        // Create a service with checkbox checked
        $service = Service::create([
            'title' => 'Test Service',
            'description' => 'Test Description',
            'is_active' => 1,
            'sort_order' => 1
        ]);

        // Update service with unchecked checkbox
        $response = $this->actingAs($this->admin)
            ->put(route('admin.services.update', $service->id), [
                'title' => 'Updated Service',
                'description' => 'Updated Description',
                'sort_order' => 2
                // Note: is_active is not sent (unchecked)
            ]);

        $response->assertRedirect();
        
        // Refresh service from database
        $service->refresh();
        
        // Assert that checkbox is now false (0)
        $this->assertEquals(0, $service->is_active);
        $this->assertEquals('Updated Service', $service->title);
    }

    /** @test */
    public function it_can_update_activity_with_unchecked_checkbox()
    {
        Storage::fake('public');
        
        // Create an activity with checkbox checked
        $activity = Activity::create([
            'title' => 'Test Activity',
            'description' => 'Test Description',
            'activity_date' => '2024-01-01',
            'image' => 'activities/default.jpg',
            'is_active' => 1,
            'sort_order' => 1
        ]);

        // Update activity with unchecked checkbox
        $response = $this->actingAs($this->admin)
            ->put(route('admin.activities.update', $activity->id), [
                'title' => 'Updated Activity',
                'description' => 'Updated Description',
                'activity_date' => '2024-02-01',
                'sort_order' => 2
                // Note: is_active is not sent (unchecked)
            ]);

        $response->assertRedirect();
        
        // Refresh activity from database
        $activity->refresh();
        
        // Assert that checkbox is now false (0)
        $this->assertEquals(0, $activity->is_active);
        $this->assertEquals('Updated Activity', $activity->title);
    }

    /** @test */
    public function it_can_create_package_with_checked_checkboxes()
    {
        Storage::fake('public');
        
        // Create package with checked checkboxes
        $response = $this->actingAs($this->admin)
            ->post(route('admin.packages.store'), [
                'name' => 'New Package',
                'description' => 'New Description',
                'features' => 'Feature 1\nFeature 2',
                'is_featured' => '1',
                'is_active' => '1',
                'sort_order' => 1
            ]);

        $response->assertRedirect();
        
        // Assert package was created with correct values
        $package = Package::where('name', 'New Package')->first();
        $this->assertNotNull($package);
        $this->assertEquals(1, $package->is_featured);
        $this->assertEquals(1, $package->is_active);
    }

    /** @test */
    public function it_can_create_package_with_unchecked_checkboxes()
    {
        Storage::fake('public');
        
        // Create package without sending checkbox values (unchecked)
        $response = $this->actingAs($this->admin)
            ->post(route('admin.packages.store'), [
                'name' => 'New Package',
                'description' => 'New Description',
                'features' => 'Feature 1\nFeature 2',
                'sort_order' => 1
                // Note: is_featured and is_active are not sent (unchecked)
            ]);

        $response->assertRedirect();
        
        // Assert package was created with false values
        $package = Package::where('name', 'New Package')->first();
        $this->assertNotNull($package);
        $this->assertEquals(0, $package->is_featured);
        $this->assertEquals(0, $package->is_active);
    }
}
